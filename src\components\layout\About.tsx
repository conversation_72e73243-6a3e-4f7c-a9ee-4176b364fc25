"use client";

import smallWhiteLogo from "../../assets/ElDev-logo-small-white.png";
import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { Separator } from "../ui/separator";
import { ScrollArea } from "../ui/scroll-area";

const About = () => {
  const marqueeOuterRef = useRef<HTMLDivElement>(null);
  const marqueeInnerRef = useRef<HTMLDivElement>(null);
  const singleSetRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    let anim: gsap.core.Tween | undefined;
    const animate = () => {
      if (!marqueeInnerRef.current || !singleSetRef.current) return;
      gsap.set(marqueeInnerRef.current, { x: 0 });
      const setWidth = singleSetRef.current.offsetWidth;
      anim?.kill();
      anim = gsap.to(marqueeInnerRef.current, {
        x: -setWidth,
        duration: 10,
        repeat: -1,
        ease: "none",
      });
    };
    animate();
    window.addEventListener("resize", animate);
    return () => {
      anim?.kill();
      window.removeEventListener("resize", animate);
    };
  }, []);

  const skillsSet = [
    {
      id: 1,
      title: "creative thinker",
    },
    {
      id: 2,
      title: "problem solver",
    },
    {
      id: 3,
      title: "A good team player",
    },
    {
      id: 4,
      title: "Passionate Learner",
    },
    
    {
      id: 5,
      title: "Resilient",
    },
  ];

  // One set of items
  const marqueeItems = (
    <div className="flex items-center gap-20" ref={singleSetRef}>
      {[...Array(4)].map((_, i) => (
        <div className="flex items-center gap-5" key={i}>
          <h2 className="text-[2rem] md:text-[4rem] font-bold tracking-tight text-zinc-400 italic">
            ElDev
          </h2>
          <div className="flex items-center justify-center relative">
            <div className="animate-spin border-[1px] border-dashed border-zinc-200 w-[100px] md:w-[200px] h-[100px] md:h-[200px] p-2 rounded-full bg-radial-[at_50%_50%] to-transparent from-zinc-800"></div>
            <img
              src={smallWhiteLogo}
              alt="logo"
              className="w-20 md:w-40 h-auto absolute"
            />
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <section className="h-auto relative py-10 mt-[100px] md:mt-[150px] z-0 bg-radial-[at_50%_53%] md:bg-radial-[at_50%_50%] from-zinc-700 via-zinc-950 via-[50%] md:via-50% to-zinc-950">
      {/* infinite horizontal scroll */}
      <div
        className="md:mt-10 w-full overflow-hidden relative"
        ref={marqueeOuterRef}
      >
        <div
          className="flex items-center"
          ref={marqueeInnerRef}
          style={{ width: "max-content" }}
        >
          {marqueeItems}
          {/* Duplicate for seamless scroll */}
          {marqueeItems}
        </div>
      </div>

      <h1 className=" mt-20 md:mt-40 text-center text-[#e4e4e7] text-[2rem] md:text-[3rem] lg:text-[3rem] font-semibold tracking-tight">
        Here's a bit about me
      </h1>

      {/* about me */}
      <div className="mt-20 px-[8px] w-[95%] md:w-[65%] mx-auto max-h-auto overflow-y-auto bg-gradient-to-b from-zinc-950/50 from-96% to-zinc-50/30 p-5 border-[1px] border-zinc-100/20 rounded-4xl backdrop-blur-lg shadow-black/50 shadow-xl ">
        <div className="bg-zinc-950/0 p-6">
          <h1 className=" leading-[24px] md:leading-[48px] text-left text-[1.2rem] md:text-[1.5rem] lg:text-[3rem] font-extrabold tracking-tight text-[#e4e4e7]">
            I am self-taught Web developer and Graphics Designer in Tanzania.
          </h1>
          <p className=" mt-4 text-left leading-[25px] md:leading-[30px] text-zinc-400 text-[1.2rem] md:text-[1.3rem]">
            I build not only websites but also moments that will capture
            people's attention.
          </p>

          <ScrollArea className=" p-[24px] mt-[48px] w-full h-[400px] border-[1px] border-zinc-100/20 rounded-4xl">
            {/* <h2 className=" text-zinc-400 pb-2 text-3xl font-bold tracking-tight">
              My skills
            </h2> */}
            <ul className=" mt-10">
              {skillsSet.map((skill, index) => (
                <li key={skill.id} className="flex flex-col items-start gap-2">
                  <div className=" flex items-center gap-2">
                    <span className=" w-10 h-10 flex items-center justify-center text-zinc-400 text-[1.3rem] bg-gradient-to-br from-zinc-950/10 from-70% to-zinc-50/50 border-[1px] border-zinc-50/10 rounded-full p-2 aspect-square">{index + 1}</span>
                    <p className="text-zinc-200 text-[1.3rem]">{skill.title}</p>
                  </div>

                  <Separator className="bg-zinc-50/20 h-[1px] w-10 my-[24px]" />
                </li>
              ))}
            </ul>
          </ScrollArea>
        </div>
      </div>
    </section>
  );
};

export default About;
