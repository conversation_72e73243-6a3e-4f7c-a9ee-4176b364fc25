import { ArrowDown } from "lucide-react";
import gsap from "gsap";
import { useEffect } from "react";
import { SplitText } from "gsap/all";

const Hero = () => {
  gsap.registerEffect({
    name: 'fadeUp',
    extendTimeLine: true,
    effect: (targets: any, config: any) => {
      let splitText = new SplitText(targets, { type: "lines" });
      return gsap.from(splitText.lines, {
        opacity: 0,
        y: 20,
        duration: config.duration,
        delay: config.delay,
        stagger: 0.2,
      })
    }
  })

  useEffect(() => {
    gsap.effects.fadeUp(".introtLine", {duration: 1, delay: 2 })

  }, []);

  return (
    <section className="h-screen relative bg-radial-[at_50%_20%] from-zinc-700 via-zinc-950 to-zinc-950 flex items-center justify-center">
      <div className=" absolute inset-0 top-[120px] left-0 h-fit w-full grid grid-cols-1 lg:grid-cols-2 mx-auto gap-10">
        <div className=" px-10 lg:pl-20 flex flex-col items-start">
          <h1 className="scroll-m-20 text-left text-[2.5rem] md:text-[3rem] lg:text-[4rem] font-extrabold tracking-tight text-[#e4e4e7] introtLine">
            Hi, I am Eliya, <br/>a Web Developer and Designer
          </h1>
        </div>
        <div></div>
      </div>
      <div className="absolute bottom-20 flex items-center w-fit">
        <p className="leading-7 text-[1.2rem] text-center text-[#a1a1aa]">
          Building comprehensive Web Applications, focusing on perfomance and
          User Experience
        </p>
      </div>
      <div className="absolute bottom-5 flex items-center w-fit animate-bounce">
        <ArrowDown color="white" />
      </div>
    </section>
  );
};

export default Hero;
