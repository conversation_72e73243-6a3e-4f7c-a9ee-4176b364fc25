import { useState } from "react";
// import smallWhiteLogo from "../../assets/ElDev-logo-small-white.png"
import smallBlackLogo from "../../assets/ElDev-logo-small-black.png";

const Navigation = () => {
  const [isScrolling, setIsScrolling] = useState(false);
  const handleScroll = () => {
    setIsScrolling(window.scrollY > 50);
  };
  window.addEventListener("scroll", handleScroll);
  return (
    <nav
      className={`fixed top-2 mx-auto p-1 py-5 w-full flex justify-center z-50 rounded-[60px] duration-300}`}
    >
      <div
        className={` border-zinc-50/10  p-5 rounded-4xl transition-all ${
          isScrolling
            ? "bg-gradient-to-b from-zinc-950/10 from-65% to-zinc-500 to-90% backdrop-blur-lg border-[1px] px-6"
            : "border-0 px-5"
        }`}
      >
        {/* <div className=" p-5 rounded-4xl"> */}
        <ul
          className={`flex items-center duration-300 transition-all ${
            isScrolling ? "gap-10" : "gap-5"
          }`}
        >
          <li>
            <a href="#" className="text-white bg-zinc-600/60 p-2 rounded-4xl">
              Home
            </a>
          </li>
          <li>
            <a href="#" className="text-white">
              Work
            </a>
          </li>
          <li>
            <a href="#" className="text-white">
              About
            </a>
          </li>
          <li>
            <a href="#" className="text-white">
              Contact
            </a>
          </li>
        </ul>
      </div>

      {/* logo */}
      <div className={` hidden md:block absolute top-4 right-20 p-1 rounded-full duration-300 ${isScrolling ? 'bg-zinc-200/50 backdrop-blur-lg' : 'bg-zinc-400 backdrop-blur-none'}`}>
            <img src={smallBlackLogo} alt="logo" className={`h-auto duration-300 ${isScrolling ? 'w-[50px]' : 'w-20'}`} />
        </div>
    </nav>
  );
};

export default Navigation;
