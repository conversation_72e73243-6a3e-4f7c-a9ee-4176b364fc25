"use client"

const Projects = () => {
    const images: any = import.meta.glob('../../assets/*.webp', { eager: true });

    const projects = [
        {
            name: "<PERSON><PERSON><PERSON>",
            description: "Hirely is a platform that connects job seekers with employers.",
            image: images['../../assets/hirely.webp'].default,
            link: "/project1"
        },
        {
            name: "IAGA",
            description: "Just another description",
            image: images['../../assets/iaga.webp'].default,
            link: "/project2"
        },
        {
            name: "Planitio",
            description: "and another description",
            image: images['../../assets/planitio.webp'].default,
            link: "/project3"
        }
    ]


  return (
    <section className="h-auto relative bg-radial-[at_100%_50%] from-zinc-700 from-10% via-zinc-950 via-20% to-zinc-950 p-10 mt-10 z-0">
        <h1 className="text-center text-[#e4e4e7] text-[2rem] md:text-[3rem] lg:text-[4rem] font-semibold tracking-tight">My Projects</h1>

        {/* projects */}
        <div className=" grid grid-cols-1 md:grid-cols-2 gap-5 mt-20">
            {projects.map((project) => (
                <div key={project.name} className="bg-[#fafafa]/20 border-[1px] border-[#a1a1aa] backdrop-blur-lg p-5 rounded-4xl">
                    <img src={project.image} alt={project.name} className="w-full h-auto rounded-4xl" />
                    <h2 className="scroll-m-20 pb-2 text-3xl font-bold tracking-tight text-zinc-100">{project.name}</h2>
                    <p className="text-[#a1a1aa] text-[1rem] font-medium tracking-tight">{project.description}</p>
                </div>
            ))}

        </div>
    </section>
  )
}

export default Projects